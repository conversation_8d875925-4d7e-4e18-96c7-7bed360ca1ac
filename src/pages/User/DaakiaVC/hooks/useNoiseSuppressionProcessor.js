import { useCallback, useEffect, useRef } from 'react';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { useNoiseSuppression } from '../context/indexContext';

export function useNoiseSuppressionProcessor({ localAudioTrack }) {
  const {
    isNoiseSuppressionEnabled,
    setIsNoiseSuppressionEnabled,
    noiseProcessor,
    isProcessing
  } = useNoiseSuppression();


  const isActivelyProcessing = useRef(false);

  const startNoiseProcessing = useCallback(async () => {
    if (!localAudioTrack || isProcessing.current || !isNoiseSuppressionEnabled || isActivelyProcessing.current) return;

    try {
      isProcessing.current = true;
      isActivelyProcessing.current = true;

      // Initialize the noise processor if it doesn't exist
      if (!noiseProcessor.current) {
        noiseProcessor.current = new NoiseSuppressionProcessor();
      }

      // Process the audio track
      const processedTrack = await noiseProcessor.current.startProcessing(
        localAudioTrack.mediaStreamTrack
      );

      if (processedTrack) {
        await localAudioTrack.replaceTrack(processedTrack, true);
        console.log('Noise suppression processing started');
      }
    } catch (error) {
      console.error('Error starting noise suppression:', error);
      // Reset the state on error
      setIsNoiseSuppressionEnabled(false);
      isActivelyProcessing.current = false;
      // Clean up the processor on error
      if (noiseProcessor.current) {
        try {
          const stopResult = noiseProcessor.current.stopProcessing();
          if (stopResult && typeof stopResult.then === 'function') {
            stopResult.catch(console.error);
          }
        } catch (cleanupError) {
          console.error('Error during cleanup after start failure:', cleanupError);
        }
      }
    } finally {
      isProcessing.current = false;
    }
  }, [localAudioTrack, isNoiseSuppressionEnabled, noiseProcessor, isProcessing, setIsNoiseSuppressionEnabled]);

  /**
   * Stop noise suppression processing and restore original track
   */
  const stopNoiseProcessing = useCallback(async () => {
    if (!localAudioTrack || isProcessing.current || !noiseProcessor.current || !isActivelyProcessing.current) return;

    try {
      isProcessing.current = true;

      // Stop the processor and restore original track
      const stopResult = noiseProcessor.current.stopProcessing();
      if (stopResult && typeof stopResult.then === 'function') {
        await stopResult;
      }

      // Get the original track
      const originalTrack = localAudioTrack.mediaStreamTrack;

      // Replace with original track
      await localAudioTrack.replaceTrack(originalTrack, true);
      console.log('Noise suppression processing stopped');
      isActivelyProcessing.current = false;
    } catch (error) {
      console.error('Error stopping noise suppression:', error);
    } finally {
      isProcessing.current = false;
    }
  }, [localAudioTrack, noiseProcessor, isProcessing]);

  /**
   * Toggle noise suppression on/off
   */
  const toggleNoiseSuppression = useCallback(async () => {
    if (isProcessing.current) return;
    
    const newState = !isNoiseSuppressionEnabled;
    setIsNoiseSuppressionEnabled(newState);
    
    if (newState) {
      await startNoiseProcessing();
    } else {
      await stopNoiseProcessing();
    }
  }, [isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled, startNoiseProcessing, stopNoiseProcessing, isProcessing]);

  // Apply noise suppression when enabled state or track changes
  useEffect(() => {
    if (!localAudioTrack) {
      // If track is not available, we can't apply noise suppression
      return;
    }

    if (isNoiseSuppressionEnabled && !isActivelyProcessing.current) {
      startNoiseProcessing();
    } else if (!isNoiseSuppressionEnabled && isActivelyProcessing.current) {
      stopNoiseProcessing();
    }

    // Cleanup on unmount
    return () => {
      if (noiseProcessor.current && isActivelyProcessing.current) {
        try {
          const result = noiseProcessor.current.stopProcessing();
          if (result && typeof result.catch === 'function') {
            result.catch(console.error);
          }
          isActivelyProcessing.current = false;
        } catch (error) {
          console.error('Error during noise processor cleanup:', error);
        }
      }
    };
  }, [isNoiseSuppressionEnabled, localAudioTrack, startNoiseProcessing, stopNoiseProcessing, noiseProcessor]);

  // Handle track changes - reapply noise suppression if track changes while enabled
  useEffect(() => {
    // If the track changes and we were actively processing, we need to restart
    if (localAudioTrack && isNoiseSuppressionEnabled && isActivelyProcessing.current) {
      // Reset the active processing state first
      isActivelyProcessing.current = false;
      // Then restart processing
      startNoiseProcessing();
    }

    // No cleanup needed for this effect
  }, [localAudioTrack?.mediaStreamTrack?.id, isNoiseSuppressionEnabled, startNoiseProcessing]);

  return {
    isNoiseSuppressionEnabled,
    toggleNoiseSuppression,
    startNoiseProcessing,
    stopNoiseProcessing
  };
}
