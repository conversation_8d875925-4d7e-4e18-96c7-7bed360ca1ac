import { useCallback, useEffect } from 'react';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { useNoiseSuppression } from '../context/indexContext';

/**
 * Hook to manage noise suppression processing for audio tracks
 * 
 * @param {Object} options - Configuration options
 * @param {Object} options.localAudioTrack - The local audio track to process
 * @param {boolean} options.enabled - Whether noise suppression should be enabled
 * @returns {Object} - Functions to control noise suppression
 */
export function useNoiseSuppressionProcessor({ localAudioTrack }) {
  const { 
    isNoiseSuppressionEnabled, 
    setIsNoiseSuppressionEnabled, 
    noiseProcessor, 
    isProcessing 
  } = useNoiseSuppression();

  /**
   * Start noise suppression processing on the audio track
   */
  const startNoiseProcessing = useCallback(async () => {
    if (!localAudioTrack || isProcessing.current || !isNoiseSuppressionEnabled) return;
    
    try {
      isProcessing.current = true;
      
      // Initialize the noise processor if it doesn't exist
      if (!noiseProcessor.current) {
        noiseProcessor.current = new NoiseSuppressionProcessor();
      }
      
      // Process the audio track
      const processedTrack = await noiseProcessor.current.startProcessing(
        localAudioTrack.mediaStreamTrack
      );
      
      if (processedTrack) {
        await localAudioTrack.replaceTrack(processedTrack, true);
        console.log('Noise suppression processing started');
      }
    } catch (error) {
      console.error('Error starting noise suppression:', error);
      setIsNoiseSuppressionEnabled(false);
    } finally {
      isProcessing.current = false;
    }
  }, [localAudioTrack, isNoiseSuppressionEnabled, noiseProcessor, isProcessing, setIsNoiseSuppressionEnabled]);

  /**
   * Stop noise suppression processing and restore original track
   */
  const stopNoiseProcessing = useCallback(async () => {
    if (!localAudioTrack || isProcessing.current || !noiseProcessor.current) return;
    
    try {
      isProcessing.current = true;
      
      // Stop the processor and restore original track
      await noiseProcessor.current.stopProcessing();
      
      // Get the original track
      const originalTrack = localAudioTrack.mediaStreamTrack;
      
      // Replace with original track
      await localAudioTrack.replaceTrack(originalTrack, true);
      console.log('Noise suppression processing stopped');
    } catch (error) {
      console.error('Error stopping noise suppression:', error);
    } finally {
      isProcessing.current = false;
    }
  }, [localAudioTrack, noiseProcessor, isProcessing]);

  /**
   * Toggle noise suppression on/off
   */
  const toggleNoiseSuppression = useCallback(async () => {
    if (isProcessing.current) return;
    
    const newState = !isNoiseSuppressionEnabled;
    setIsNoiseSuppressionEnabled(newState);
    
    if (newState) {
      await startNoiseProcessing();
    } else {
      await stopNoiseProcessing();
    }
  }, [isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled, startNoiseProcessing, stopNoiseProcessing, isProcessing]);

  // Apply noise suppression when enabled state or track changes
  useEffect(() => {
    if (isNoiseSuppressionEnabled && localAudioTrack) {
      startNoiseProcessing();
    } else if (!isNoiseSuppressionEnabled && localAudioTrack && noiseProcessor.current) {
      stopNoiseProcessing();
    }
    
    // Cleanup on unmount
    return () => {
      if (noiseProcessor.current) {
        noiseProcessor.current.stopProcessing().catch(console.error);
      }
    };
  }, [isNoiseSuppressionEnabled, localAudioTrack, startNoiseProcessing, stopNoiseProcessing, noiseProcessor]);

  return {
    isNoiseSuppressionEnabled,
    toggleNoiseSuppression,
    startNoiseProcessing,
    stopNoiseProcessing
  };
}
