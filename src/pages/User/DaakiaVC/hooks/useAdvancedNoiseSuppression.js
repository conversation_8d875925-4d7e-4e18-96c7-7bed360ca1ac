import { useCallback, useEffect } from 'react';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { Track } from 'livekit-client';
import { useMaybeRoomContext } from '@livekit/components-react';
import { useNoiseSuppressionContext } from '../context/indexContext';

/**
 * Advanced Noise Suppression Hook
 * 
 * Handles all edge cases:
 * - Device switching
 * - Track replacement
 * - Microphone on/off states
 * - Proper cleanup and error recovery
 * - State synchronization
 */
export function useAdvancedNoiseSuppression() {
  const room = useMaybeRoomContext();
  const {
    isNoiseSuppressionOn,
    isNoiseProcessing,
    isNoiseSuppressionActive,
    setIsNoiseProcessing,
    setIsNoiseSuppressionActive,
    noiseProcessorRef,
    processingStateRef,
    currentTrackIdRef,
    toggleNoiseSuppressionOn,
    toggleNoiseSuppressionOff,
    toggleNoiseSuppression,
  } = useNoiseSuppressionContext();

  // Get current audio track
  const getAudioTrack = useCallback(() => {
    if (!room?.localParticipant) return null;
    const trackPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
    return trackPublication?.track || null;
  }, [room]);

  // Check if microphone is enabled
  const isMicrophoneEnabled = useCallback(() => {
    const track = getAudioTrack();
    return track?.isEnabled && track?.mediaStreamTrack?.readyState === 'live';
  }, [getAudioTrack]);

  /**
   * Clean up processor completely
   */
  const cleanupProcessor = useCallback(async () => {
    console.log('🧹 Cleaning up noise processor...');
    
    if (noiseProcessorRef.current && processingStateRef.current !== 'idle') {
      try {
        processingStateRef.current = 'stopping';
        
        // Stop processing if active
        const stopResult = noiseProcessorRef.current.stopProcessing();
        if (stopResult && typeof stopResult.then === 'function') {
          await stopResult;
        }
        
        console.log('✅ Noise processor stopped successfully');
      } catch (error) {
        console.warn('⚠️ Error during processor cleanup (ignoring):', error);
      }
    }
    
    // Reset all state
    noiseProcessorRef.current = null;
    processingStateRef.current = 'idle';
    currentTrackIdRef.current = null;
    setIsNoiseSuppressionActive(false);
    
    console.log('🧹 Processor cleanup complete');
  }, [noiseProcessorRef, processingStateRef, currentTrackIdRef, setIsNoiseSuppressionActive]);

  /**
   * Start noise suppression processing
   */
  const startNoiseProcessing = useCallback(async () => {
    console.log('🎵 Starting noise suppression...');
    
    // Prevent concurrent operations
    if (processingStateRef.current !== 'idle') {
      console.log('⏳ Processing already in progress, skipping...');
      return false;
    }
    
    const audioTrack = getAudioTrack();
    if (!audioTrack || !isMicrophoneEnabled()) {
      console.log('❌ No valid audio track available');
      return false;
    }
    
    const trackId = audioTrack.mediaStreamTrack.id;
    
    try {
      processingStateRef.current = 'starting';
      setIsNoiseProcessing(true);
      
      // Always cleanup first to ensure clean state
      await cleanupProcessor();
      
      // Create fresh processor instance
      console.log('🔧 Creating new NoiseSuppressionProcessor...');
      noiseProcessorRef.current = new NoiseSuppressionProcessor();
      
      // Start processing
      console.log('🎯 Starting processing on track:', trackId);
      const processedTrack = await noiseProcessorRef.current.startProcessing(
        audioTrack.mediaStreamTrack
      );
      
      if (processedTrack) {
        // Replace the track with processed version
        await audioTrack.replaceTrack(processedTrack, true);
        
        // Update state
        processingStateRef.current = 'active';
        currentTrackIdRef.current = trackId;
        setIsNoiseSuppressionActive(true);
        
        console.log('✅ Noise suppression started successfully');
        return true;
      } else {
        throw new Error('Failed to get processed track');
      }
      
    } catch (error) {
      console.error('❌ Error starting noise suppression:', error);
      
      // Clean up on error
      await cleanupProcessor();
      
      return false;
    } finally {
      setIsNoiseProcessing(false);
    }
  }, [
    getAudioTrack,
    isMicrophoneEnabled,
    cleanupProcessor,
    noiseProcessorRef,
    processingStateRef,
    currentTrackIdRef,
    setIsNoiseProcessing,
    setIsNoiseSuppressionActive
  ]);

  /**
   * Stop noise suppression processing
   */
  const stopNoiseProcessing = useCallback(async () => {
    console.log('🛑 Stopping noise suppression...');
    
    if (processingStateRef.current === 'idle') {
      console.log('💤 Already idle, nothing to stop');
      return true;
    }
    
    const audioTrack = getAudioTrack();
    if (!audioTrack) {
      console.log('❌ No audio track available for restoration');
      await cleanupProcessor();
      return true;
    }
    
    try {
      setIsNoiseProcessing(true);
      
      // Stop the processor
      await cleanupProcessor();
      
      // Create fresh audio stream to replace the processed one
      console.log('🔄 Creating fresh audio stream...');
      const settings = audioTrack.mediaStreamTrack.getSettings();
      const constraints = {
        audio: {
          deviceId: settings.deviceId || 'default',
          echoCancellation: false,
          noiseSuppression: false,
        }
      };
      
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      const newTrack = stream.getAudioTracks()[0];
      
      // Replace with fresh track
      await audioTrack.replaceTrack(newTrack, true);
      
      console.log('✅ Noise suppression stopped successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Error stopping noise suppression:', error);
      await cleanupProcessor();
      return false;
    } finally {
      setIsNoiseProcessing(false);
    }
  }, [getAudioTrack, cleanupProcessor, setIsNoiseProcessing]);

  /**
   * Handle track changes (device switching, mic on/off)
   */
  const handleTrackChange = useCallback(async () => {
    const audioTrack = getAudioTrack();
    const currentTrackId = audioTrack?.mediaStreamTrack?.id;
    
    // If track changed and we were processing, restart
    if (
      isNoiseSuppressionOn &&
      isNoiseSuppressionActive &&
      currentTrackId &&
      currentTrackId !== currentTrackIdRef.current
    ) {
      console.log('🔄 Track changed, restarting noise suppression...');
      console.log('Old track:', currentTrackIdRef.current);
      console.log('New track:', currentTrackId);
      
      await cleanupProcessor();
      if (isMicrophoneEnabled()) {
        await startNoiseProcessing();
      }
    }
  }, [
    getAudioTrack,
    isNoiseSuppressionOn,
    isNoiseSuppressionActive,
    currentTrackIdRef,
    cleanupProcessor,
    startNoiseProcessing,
    isMicrophoneEnabled
  ]);

  // Main effect: Handle noise suppression toggle
  useEffect(() => {
    const handleNoiseSuppressionToggle = async () => {
      if (!room || room.state !== 'connected') return;
      
      if (isNoiseSuppressionOn && !isNoiseSuppressionActive && isMicrophoneEnabled()) {
        // Turn ON noise suppression
        await startNoiseProcessing();
      } else if (!isNoiseSuppressionOn && isNoiseSuppressionActive) {
        // Turn OFF noise suppression
        await stopNoiseProcessing();
      }
    };
    
    handleNoiseSuppressionToggle();
  }, [
    room,
    room?.state,
    isNoiseSuppressionOn,
    isNoiseSuppressionActive,
    isMicrophoneEnabled,
    startNoiseProcessing,
    stopNoiseProcessing
  ]);

  // Effect: Handle track changes
  useEffect(() => {
    handleTrackChange();
  }, [handleTrackChange, getAudioTrack()?.mediaStreamTrack?.id]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupProcessor();
    };
  }, [cleanupProcessor]);

  return {
    // State
    isNoiseSuppressionOn,
    isNoiseProcessing,
    isNoiseSuppressionActive,
    
    // Actions
    toggleNoiseSuppressionOn,
    toggleNoiseSuppressionOff,
    toggleNoiseSuppression,
    
    // Manual controls (for advanced usage)
    startNoiseProcessing,
    stopNoiseProcessing,
    cleanupProcessor,
    
    // Utilities
    isMicrophoneEnabled: isMicrophoneEnabled(),
    currentAudioTrack: getAudioTrack(),
  };
}
