import React, { createContext, useContext, useState, useMemo, useRef } from 'react'

const VirtualBackgroundContext = createContext()
export function VirtualBackgroundProvider({ children }) {
  const [currentEffect, setCurrentEffect] = useState(null)
  const isApplyingEffect = useRef(false) // Prevent multiple simultaneous applications

  const contextValue = useMemo(() => ({
    currentEffect,
    setCurrentEffect,
    isApplyingEffect,
  }), [currentEffect])

  return (
    <VirtualBackgroundContext.Provider value={contextValue}>
      {children}
    </VirtualBackgroundContext.Provider>
  )
}

// Advanced Noise Suppression Context
const NoiseSuppressionContext = createContext()
export function NoiseSuppressionProvider({ children }) {
  // Main toggle state - whether noise suppression is ON or OFF
  const [isNoiseSuppressionOn, setIsNoiseSuppressionOn] = useState(false)

  // Processing states
  const [isNoiseProcessing, setIsNoiseProcessing] = useState(false)
  const [isNoiseSuppressionActive, setIsNoiseSuppressionActive] = useState(false)

  // Processor instance and state tracking
  const noiseProcessorRef = useRef(null)
  const processingStateRef = useRef('idle') // 'idle' | 'starting' | 'active' | 'stopping'
  const currentTrackIdRef = useRef(null) // Track which audio track we're processing

  // Toggle functions with clear naming
  const toggleNoiseSuppressionOn = () => setIsNoiseSuppressionOn(true)
  const toggleNoiseSuppressionOff = () => setIsNoiseSuppressionOn(false)
  const toggleNoiseSuppression = () => setIsNoiseSuppressionOn(prev => !prev)

  const contextValue = useMemo(() => ({
    // State
    isNoiseSuppressionOn,
    isNoiseProcessing,
    isNoiseSuppressionActive,

    // Toggle functions
    setIsNoiseSuppressionOn,
    toggleNoiseSuppressionOn,
    toggleNoiseSuppressionOff,
    toggleNoiseSuppression,

    // Processing state setters
    setIsNoiseProcessing,
    setIsNoiseSuppressionActive,

    // Refs for advanced control
    noiseProcessorRef,
    processingStateRef,
    currentTrackIdRef,
  }), [isNoiseSuppressionOn, isNoiseProcessing, isNoiseSuppressionActive])

  return (
    <NoiseSuppressionContext.Provider value={contextValue}>
      {children}
    </NoiseSuppressionContext.Provider>
  )
}


export function IndexProvider({ children }) {
  return (
    <VirtualBackgroundProvider>
      <NoiseSuppressionProvider>
        {children}
      </NoiseSuppressionProvider>
    </VirtualBackgroundProvider>
  )
}

export function useVirtualBackground() {
  const context = useContext(VirtualBackgroundContext)
  if (!context) throw new Error('useVirtualBackground must be used within VirtualBackgroundProvider')
  return context
}

export function useNoiseSuppressionContext() {
  const context = useContext(NoiseSuppressionContext)
  if (!context) throw new Error('useNoiseSuppressionContext must be used within NoiseSuppressionProvider')
  return context
}

export default VirtualBackgroundContext