import React from 'react';
import Switch from '../../../components/Antd/Switch/index.ant';
import { useAdvancedNoiseSuppression } from '../hooks/useAdvancedNoiseSuppression';
import './NoiseSuppressionToggle.scss';

/**
 * Advanced Noise Suppression Toggle Component
 * 
 * Features:
 * - Smart state management
 * - Automatic error recovery
 * - Visual feedback for processing states
 * - Disabled when microphone is off
 */
function NoiseSuppressionToggle({ 
  className = '',
  showLabel = true,
  showDescription = true,
  disabled = false 
}) {
  const {
    isNoiseSuppressionOn,
    isNoiseProcessing,
    isNoiseSuppressionActive,
    isMicrophoneEnabled,
    toggleNoiseSuppression,
  } = useAdvancedNoiseSuppression();

  // Determine if toggle should be disabled
  const isDisabled = disabled || !isMicrophoneEnabled || isNoiseProcessing;

  // Get status text for user feedback
  const getStatusText = () => {
    if (!isMicrophoneEnabled) {
      return 'Microphone is off';
    }
    if (isNoiseProcessing) {
      return isNoiseSuppressionOn ? 'Enabling...' : 'Disabling...';
    }
    if (isNoiseSuppressionOn && isNoiseSuppressionActive) {
      return 'Active';
    }
    if (isNoiseSuppressionOn && !isNoiseSuppressionActive) {
      return 'Pending';
    }
    return 'Disabled';
  };

  const handleToggle = () => {
    if (!isDisabled) {
      toggleNoiseSuppression();
    }
  };

  return (
    <div className={`noise-suppression-toggle ${className}`}>
      <div className="noise-suppression-control">
        {showLabel && (
          <div className="noise-suppression-label">
            <span className="label-text">Noise Suppression</span>
            {showDescription && (
              <small className="label-description">
                Remove background noise from your microphone
              </small>
            )}
          </div>
        )}
        
        <div className="noise-suppression-switch-container">
          <Switch
            checked={isNoiseSuppressionOn}
            onChange={handleToggle}
            disabled={isDisabled}
          />
          
          {/* Status indicator */}
          <div className={`status-indicator ${getStatusText().toLowerCase().replace(/\s+/g, '-')}`}>
            <span className="status-text">{getStatusText()}</span>
            
            {/* Processing spinner */}
            {isNoiseProcessing && (
              <div className="processing-spinner" />
            )}
          </div>
        </div>
      </div>
      
      {/* Debug info (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="debug-info">
          <small>
            On: {isNoiseSuppressionOn ? '✅' : '❌'} | 
            Active: {isNoiseSuppressionActive ? '✅' : '❌'} | 
            Processing: {isNoiseProcessing ? '⏳' : '✅'} | 
            Mic: {isMicrophoneEnabled ? '🎤' : '🔇'}
          </small>
        </div>
      )}
    </div>
  );
}

export default NoiseSuppressionToggle;
