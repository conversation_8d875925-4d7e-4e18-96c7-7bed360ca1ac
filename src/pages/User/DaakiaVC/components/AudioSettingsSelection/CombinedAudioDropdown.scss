// SCSS Variables - Colors from ControlBar.scss and VideoConference.scss
$primary-bg-dark: #000;
$secondary-bg-dark: #1f1f1f;
$tertiary-bg-dark: #2b2b2b;
$quaternary-bg-dark: #2d2d38;
$border-dark: #111;
$border-secondary: #242424;
$accent-blue: #0a84ff;
$accent-blue-hover: #40a9ff;
$white: #fff;
$text-primary: #ffffff;
$text-secondary: #818088;
$hover-overlay: rgba(255, 255, 255, 0.1);
$shadow-dark: rgba(0, 0, 0, 0.75);
$backdrop-blur: rgba(0, 0, 0, 0.7);

.combined-audio-dropdown-button {
  background: transparent;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:focus {
    outline: 2px solid $accent-blue;
    outline-offset: 2px;
  }
}

.combined-audio-dropdown-menu {
  background: $secondary-bg-dark;
  border-radius: 8px;
  box-shadow: 0px 0px 5px 0px $shadow-dark;
  min-width: 400px;
  padding: 0.5rem;
  border: 1px solid $border-secondary;
  color: $text-primary;
  margin-bottom: 8px;
  max-height: 300px;
  overflow-y: auto;

  // Custom scrollbar styling to match SettingsModal
  &::-webkit-scrollbar {
    width: 2px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #798593;
    padding: 0.3rem;
    border-radius: 0.3rem;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background-color: #6b7a8a;
  }
}

.audio-settings-row {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.audio-setting-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-dropdown {
  width: 100%;

  .audio-device-dropdown-button,
  .speaker-device-dropdown-button {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid $border-secondary;
    border-radius: 6px;
    background: $secondary-bg-dark;
    color: $text-primary;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    transition: all 0.2s ease;

    &:hover {
      background-color: $hover-overlay;
      border-color: $accent-blue-hover;
    }

    &:focus {
      outline: none;
      border-color: $accent-blue;
      background-color: $hover-overlay;
    }
  }
}

// Noise Suppression Section
.noise-suppression-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  margin-top: 12px;
  border-top: 1px solid $border-secondary;

  .noise-suppression-label {
    display: flex;
    flex-direction: column;
    gap: 2px;

    span {
      font-size: 14px;
      font-weight: 500;
      color: $text-primary;
    }

    small {
      font-size: 12px;
      color: $text-secondary;
    }
  }
}

// Responsive design for smaller screens
@media (max-width: 768px) {
  .combined-audio-dropdown-menu {
    min-width: 300px;
    padding: 12px;
  }

  .audio-settings-row {
    flex-direction: column;
    gap: 16px;
  }
}

// Light theme fallback (maintaining original light theme colors)
[data-lk-theme="light"] {
  .combined-audio-dropdown-menu {
    background: $white;
    border-color: #d9d9d9;
    color: #000;
  }

  .device-dropdown {
    .audio-device-dropdown-button,
    .speaker-device-dropdown-button {
      background: $white;
      border-color: #d9d9d9;
      color: #000;

      &:hover {
        border-color: $accent-blue-hover;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

      &:focus {
        border-color: $accent-blue;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
}
