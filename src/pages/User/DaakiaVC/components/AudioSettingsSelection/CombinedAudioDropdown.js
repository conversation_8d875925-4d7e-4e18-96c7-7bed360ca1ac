import React from "react";
import { Dropdown } from "antd";
import { useMaybeRoomContext } from "@livekit/components-react";
import { Track } from "livekit-client";
import AudioDeviceDropdown from "./AudioDeviceDropdown";
import SpeakerDeviceDropdown from "./SpeakerDeviceDropdown";
import Switch from "../../../../components/Antd/Switch/index.ant";
import { useNoiseSuppression } from "../../context/indexContext";
import { useNoiseSuppressionProcessor } from "../../hooks/useNoiseSuppressionProcessor";
import "./CombinedAudioDropdown.scss";

const CombinedAudioDropdown = React.memo(function CombinedAudioDropdown({
  micSelection = undefined,
  speakerSelection = undefined,
  onMicChange = undefined,
  onSpeakerChange = undefined,
  className = ""
}) {
  const room = useMaybeRoomContext();
  const { isNoiseSuppressionEnabled } = useNoiseSuppression();

  // Get the local audio track
  const localAudioTrack = room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.track;

  // Use the noise suppression processor hook
  const { toggleNoiseSuppression } = useNoiseSuppressionProcessor({
    localAudioTrack
  });

  const dropdownContent = (
    <div className="combined-audio-dropdown-menu">
      <div className="audio-settings-row">
        {/* Microphone Section */}
        <div className="audio-setting-section">
          <AudioDeviceDropdown
            kind="audioinput"
            onActiveDeviceChange={onMicChange}
            initialSelection={micSelection}
            className="device-dropdown"
          />
        </div>
        <div className="audio-setting-section">
          <SpeakerDeviceDropdown
            kind="audiooutput"
            onActiveDeviceChange={onSpeakerChange}
            initialSelection={speakerSelection}
            className="device-dropdown"
          />
        </div>
      </div>

      {/* Noise Suppression Section */}
      <div className="noise-suppression-section">
        <div className="noise-suppression-label">
          <span>Noise Suppression</span>
          <small>Remove background noise from your microphone</small>
        </div>
        <Switch
          checked={isNoiseSuppressionEnabled}
          onChange={toggleNoiseSuppression}
          disabled={!localAudioTrack}
        />
      </div>
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
    >
      <button
        className="combined-audio-dropdown-button"
        type="button"
        aria-label="Audio settings - microphone and speaker selection"
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        <span>▼</span>
      </button>
    </Dropdown>
  );
});





export default CombinedAudioDropdown;
