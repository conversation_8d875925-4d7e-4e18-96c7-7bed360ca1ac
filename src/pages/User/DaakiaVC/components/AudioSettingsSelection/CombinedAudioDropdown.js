import React from "react";
import { Dropdown } from "antd";
import AudioDeviceDropdown from "./AudioDeviceDropdown";
import SpeakerDeviceDropdown from "./SpeakerDeviceDropdown";
import NoiseSuppressionToggle from "../NoiseSuppressionToggle";
import "./CombinedAudioDropdown.scss";

// Chevron down icon component
function ChevronDownIcon() {
  return (
    <svg width="12" height="6" viewBox="0 0 12 6" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1 1L6 5L11 1" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
}

const CombinedAudioDropdown = React.memo(function CombinedAudioDropdown({
  micSelection = undefined,
  speakerSelection = undefined,
  onMicChange = undefined,
  onSpeakerChange = undefined,
  className = ""
}) {

  const dropdownContent = (
    <div className="combined-audio-dropdown-menu">
      <div className="audio-settings-row">
        {/* Microphone Section */}
        <div className="audio-setting-section">
          <AudioDeviceDropdown
            kind="audioinput"
            onActiveDeviceChange={onMicChange}
            initialSelection={micSelection}
            className="device-dropdown"
          />
        </div>
        <div className="audio-setting-section">
          <SpeakerDeviceDropdown
            kind="audiooutput"
            onActiveDeviceChange={onSpeakerChange}
            initialSelection={speakerSelection}
            className="device-dropdown"
          />
        </div>
      </div>

      {/* Advanced Noise Suppression Section */}
      <NoiseSuppressionToggle />
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
    >
      <button
        className="combined-audio-dropdown-button"
        type="button"
        aria-label="Audio settings - microphone and speaker selection"
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        <ChevronDownIcon />
      </button>
    </Dropdown>
  );
});

export default CombinedAudioDropdown;
