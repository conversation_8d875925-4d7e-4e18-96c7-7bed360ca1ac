// SCSS Variables - Colors from existing components
$primary-bg-dark: #000;
$secondary-bg-dark: #1f1f1f;
$tertiary-bg-dark: #2b2b2b;
$border-dark: #111;
$border-secondary: #242424;
$accent-blue: #0a84ff;
$accent-green: #30d158;
$accent-orange: #ff9f0a;
$accent-red: #ff453a;
$white: #fff;
$text-primary: #ffffff;
$text-secondary: #818088;
$text-muted: #666;

.noise-suppression-toggle {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .noise-suppression-control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    
    .noise-suppression-label {
      display: flex;
      flex-direction: column;
      gap: 4px;
      flex: 1;
      
      .label-text {
        font-size: 14px;
        font-weight: 500;
        color: $text-primary;
        line-height: 1.2;
      }
      
      .label-description {
        font-size: 12px;
        color: $text-secondary;
        line-height: 1.3;
      }
    }
    
    .noise-suppression-switch-container {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .status-indicator {
        display: flex;
        align-items: center;
        gap: 6px;
        min-width: 80px;
        
        .status-text {
          font-size: 11px;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
        
        // Status colors
        &.active .status-text {
          color: $accent-green;
        }
        
        &.pending .status-text {
          color: $accent-orange;
        }
        
        &.enabling .status-text,
        &.disabling .status-text {
          color: $accent-blue;
        }
        
        &.disabled .status-text,
        &.microphone-is-off .status-text {
          color: $text-muted;
        }
        
        // Processing spinner
        .processing-spinner {
          width: 12px;
          height: 12px;
          border: 2px solid transparent;
          border-top: 2px solid $accent-blue;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
    }
  }
  
  // Debug info styling
  .debug-info {
    padding: 8px;
    background: rgba($accent-blue, 0.1);
    border-radius: 4px;
    border: 1px solid rgba($accent-blue, 0.2);
    
    small {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 10px;
      color: $accent-blue;
      line-height: 1.4;
    }
  }
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .noise-suppression-toggle {
    .noise-suppression-control {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      
      .noise-suppression-switch-container {
        align-self: stretch;
        justify-content: space-between;
      }
    }
  }
}

// Light theme support
[data-lk-theme="light"] {
  .noise-suppression-toggle {
    .noise-suppression-label {
      .label-text {
        color: #000;
      }
      
      .label-description {
        color: #666;
      }
    }
    
    .debug-info {
      background: rgba($accent-blue, 0.05);
      border-color: rgba($accent-blue, 0.15);
    }
  }
}

// Integration with existing dropdown styles
.combined-audio-dropdown-menu {
  .noise-suppression-toggle {
    border-top: 1px solid $border-secondary;
    margin-top: 12px;
    padding-top: 12px;
    
    .noise-suppression-control {
      padding: 0;
    }
  }
}
