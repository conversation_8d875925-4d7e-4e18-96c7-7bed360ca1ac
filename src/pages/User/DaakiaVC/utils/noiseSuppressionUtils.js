import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';

export async function applyNoiseSuppression(localAudioTrack, processor) {
  if (!localAudioTrack || !processor) {
    console.warn('Missing audio track or processor for noise suppression');
    return false;
  }

  try {
    const processedTrack = await processor.startProcessing(
      localAudioTrack.mediaStreamTrack
    );
    
    if (processedTrack) {
      await localAudioTrack.replaceTrack(processedTrack, true);
      console.log('Noise suppression applied successfully');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error applying noise suppression:', error);
    return false;
  }
}


export async function removeNoiseSuppression(localAudioTrack, processor) {
  if (!localAudioTrack || !processor) {
    console.warn('Missing audio track or processor for noise suppression removal');
    return false;
  }

  try {
    await processor.stopProcessing();
    
    // Get the original track
    const originalTrack = localAudioTrack.mediaStreamTrack;
    
    // Replace with original track
    await localAudioTrack.replaceTrack(originalTrack, true);
    console.log('Noise suppression removed successfully');
    return true;
  } catch (error) {
    console.error('Error removing noise suppression:', error);
    return false;
  }
}


export function createNoiseProcessor() {
  try {
    return new NoiseSuppressionProcessor();
  } catch (error) {
    console.error('Error creating noise suppression processor:', error);
    return null;
  }
}


export async function toggleNoiseSuppression(localAudioTrack, processor, enable) {
  if (enable) {
    return await applyNoiseSuppression(localAudioTrack, processor);
  } else {
    return await removeNoiseSuppression(localAudioTrack, processor);
  }
}
